"""
Base Module Installer - Handles base module installation process

This module provides the BaseModuleInstaller class for installing the base module
using modern component-based approach with clean separation of concerns.
"""

from typing import TYPE_CHECKING

from ...logging import get_logger
from .installation_context import InstallationContext, InstallationUtilities

if TYPE_CHECKING:
    from ...environment import Environment


class BaseModuleInstaller:
    """
    Modern base module installer with clean separation of concerns.

    This installer handles base module installation without backward compatibility
    or legacy patterns. It uses dedicated components for each responsibility:
    - BaseSchemaManager: Database schema operations
    - IRPopulationManager: Shared IR metadata population (same as regular modules)
    - BaseModuleRegistrar: Module registration
    - Shared RegistryUpdater: Registry management (reusable utility)
    - Shared ValidationManager: Installation validation (reusable utility)

    No hooks are used for base module installation to ensure clean bootstrap.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.utilities = InstallationUtilities()

        # Initialize component managers
        self._schema_manager = None
        self._module_registrar = None

    def _get_schema_manager(self):
        """Lazy initialization of schema manager"""
        if self._schema_manager is None:
            import importlib.util
            import sys

            from erp.addons.utils.path_resolver import get_addon_path_resolver

            path_resolver = get_addon_path_resolver()
            base_path = path_resolver.find_addon_path("base")
            if not base_path:
                raise ImportError("Base addon not found")

            # Dynamic import of BaseSchemaManager
            schema_manager_path = path_resolver.resolve_relative_path(
                "base", "schema_manager.py"
            )
            if not schema_manager_path:
                raise ImportError("BaseSchemaManager module not found")

            spec = importlib.util.spec_from_file_location(
                "base.schema_manager", schema_manager_path
            )
            module = importlib.util.module_from_spec(spec)
            sys.modules["base.schema_manager"] = module
            spec.loader.exec_module(module)

            self._schema_manager = module.BaseSchemaManager()
        return self._schema_manager

    def _get_module_registrar(self):
        """Lazy initialization of module registrar"""
        if self._module_registrar is None:
            import importlib.util
            import sys

            from erp.addons.utils.path_resolver import get_addon_path_resolver

            path_resolver = get_addon_path_resolver()
            base_path = path_resolver.find_addon_path("base")
            if not base_path:
                raise ImportError("Base addon not found")

            # Dynamic import of BaseModuleRegistrar
            module_registrar_path = path_resolver.resolve_relative_path(
                "base", "module_registrar.py"
            )
            if not module_registrar_path:
                raise ImportError("BaseModuleRegistrar module not found")

            spec = importlib.util.spec_from_file_location(
                "base.module_registrar", module_registrar_path
            )
            module = importlib.util.module_from_spec(spec)
            sys.modules["base.module_registrar"] = module
            spec.loader.exec_module(module)

            self._module_registrar = module.BaseModuleRegistrar()
        return self._module_registrar

    async def install_base_module(self, env: "Environment") -> bool:
        """
        Install the base module using modern component-based approach.

        This method orchestrates the installation process using dedicated
        components for each responsibility, ensuring clean separation of
        concerns and no backward compatibility dependencies.

        Args:
            env: Environment for database operations

        Returns:
            True if installation successful, False otherwise
        """
        context = InstallationContext("base", env, "install")
        context.logger.info(
            "🔧 Installing base module with modern component-based installer"
        )

        try:
            # Step 1: Create database schema
            context.log_step("Step 1: Creating base database schema...")
            schema_manager = self._get_schema_manager()
            success = await schema_manager.create_base_schema(env.cr)
            if not success:
                context.logger.error("Failed to create base database schema")
                return False

            # Step 2: Populate IR metadata
            context.log_step("Step 2: Populating IR metadata...")
            from .components.ir_metadata_populator import IRMetadataPopulator

            # Use the reusable IR metadata populator
            ir_populator = IRMetadataPopulator()
            ir_results = await self._populate_base_ir_metadata_with_component(
                env.cr, ir_populator
            )

            if ir_results.get("status") == "error":
                context.logger.error(
                    f"Failed to populate IR metadata: {ir_results.get('message')}"
                )
                return False

            # Step 2.5: Force load addon models to AppRegistry after IR population
            context.log_step("Step 2.5: Loading addon models to AppRegistry...")
            try:
                from erp.memory.registry import AppRegistry

                # Get the app registry instance
                app_registry = AppRegistry.get_instance()

                # Force load the base addon models to the registry
                await app_registry.force_load_addon("base")
                context.logger.info("✓ Successfully loaded base addon models to AppRegistry")

            except Exception as e:
                context.logger.error(f"Failed to load addon models to AppRegistry: {e}")
                return False

            # Step 3: Load XML data files
            context.log_step("Step 3: Loading XML data files...")
            from .components.xml_data_loader import XMLDataLoader

            xml_loader = XMLDataLoader()
            # Enable strict XML ID validation to catch missing references early
            xml_result = await xml_loader.load_addon_data_files(
                env.cr._db_manager, "base", strict_xmlid_validation=True
            )

            if not xml_result.get("success", False):
                context.logger.error(
                    f"Failed to load XML data files: {xml_result.get('error', 'Unknown error')}"
                )
                return False

            # Step 4: Register base module
            context.log_step("Step 4: Registering base module...")
            module_registrar = self._get_module_registrar()
            success = await module_registrar.register_base_module(env.cr)
            if not success:
                context.logger.error("Failed to register base module")
                return False

            # Step 5: Update registry
            context.log_step("Step 5: Registry update handled by environment...")

            # Step 6: Validate installation
            context.log_step("Step 6: Validating installation...")

            # Use centralized lifecycle manager for temporary ModelRegistry
            context.log_step("Creating temporary ModelRegistry for base addon...")
            lifecycle_manager = self.utilities.get_lifecycle_manager()

            async with lifecycle_manager.temporary_registry_context(
                "base"
            ) as model_registry:
                # Extract base module requirements from ModelRegistry
                base_tables = (
                    self.utilities.registry_extractor.extract_tables_from_registry(
                        model_registry
                    )
                )
                base_models = (
                    self.utilities.registry_extractor.extract_models_from_registry(
                        model_registry
                    )
                )
                base_constraints = (
                    self.utilities.registry_extractor.extract_constraints_from_registry(
                        model_registry
                    )
                )
                base_indexes = (
                    self.utilities.registry_extractor.extract_indexes_from_registry(
                        model_registry
                    )
                )

                context.logger.debug(
                    f"Extracted from ModelRegistry - Tables: {len(base_tables)}, "
                    f"Models: {len(base_models)}, Constraints: {len(base_constraints)}, "
                    f"Indexes: {len(base_indexes)}"
                )

                # Use shared validation manager with dynamically extracted requirements
                validation_manager = self.utilities.get_validation_manager()
                success = (
                    await validation_manager.validate_tables_exist(env.cr, base_tables)
                    and await validation_manager.validate_constraints_exist(
                        env.cr, base_constraints
                    )
                    and await validation_manager.validate_indexes_exist(
                        env.cr, base_indexes
                    )
                    and await validation_manager.validate_models_in_ir_metadata(
                        env.cr, base_models
                    )
                    and await validation_manager.validate_module_registration(
                        env.cr, "base"
                    )
                )

            if not success:
                context.logger.error("Installation validation failed")
                return False

            context.log_success()
            return True

        except Exception as e:
            context.log_error(e)
            return False

    async def get_installation_report(self, env: "Environment") -> dict:
        """
        Get detailed installation report for debugging purposes.

        Args:
            env: Environment for database operations

        Returns:
            Dictionary containing detailed validation report
        """
        try:
            # Use centralized lifecycle manager for temporary ModelRegistry
            lifecycle_manager = self.utilities.get_lifecycle_manager()

            async with lifecycle_manager.temporary_registry_context(
                "base"
            ) as model_registry:
                # Extract base module requirements from ModelRegistry
                base_tables = (
                    self.utilities.registry_extractor.extract_tables_from_registry(
                        model_registry
                    )
                )
                base_models = (
                    self.utilities.registry_extractor.extract_models_from_registry(
                        model_registry
                    )
                )
                base_constraints = (
                    self.utilities.registry_extractor.extract_constraints_from_registry(
                        model_registry
                    )
                )
                base_indexes = (
                    self.utilities.registry_extractor.extract_indexes_from_registry(
                        model_registry
                    )
                )

                # Use shared validation manager with dynamically extracted requirements
                validation_manager = self.utilities.get_validation_manager()
                return await validation_manager.get_validation_report(
                    env.cr,
                    env,
                    "base",
                    base_tables,
                    base_models,
                    base_constraints,
                    base_indexes,
                )
        except Exception as e:
            self.logger.error(f"Failed to generate installation report: {e}")
            return {"error": str(e), "overall_valid": False}

    async def _populate_base_ir_metadata_with_component(
        self, db_manager: "DatabaseManager", ir_populator
    ) -> dict:
        """
        Populate IR metadata for base models using the reusable IR metadata populator component.
        """
        try:
            # Dynamic import of BaseSchemaManager
            import importlib.util
            import sys

            from erp.addons.utils.path_resolver import get_addon_path_resolver

            path_resolver = get_addon_path_resolver()
            schema_manager_path = path_resolver.resolve_relative_path(
                "base", "schema_manager.py"
            )
            if not schema_manager_path:
                raise ImportError("BaseSchemaManager module not found")

            spec = importlib.util.spec_from_file_location(
                "base.schema_manager", schema_manager_path
            )
            module = importlib.util.module_from_spec(spec)
            if "base.schema_manager" not in sys.modules:
                sys.modules["base.schema_manager"] = module
                spec.loader.exec_module(module)
            else:
                module = sys.modules["base.schema_manager"]

            # Get discovered base models from BaseSchemaManager
            schema_manager = module.BaseSchemaManager()
            discovered_models = schema_manager.get_discovered_models()

            # Use the reusable IR metadata populator
            return await ir_populator.populate_addon_ir_metadata(
                db_manager, "base", discovered_models
            )

        except Exception as e:
            self.logger.error(f"Failed to populate base IR metadata: {e}")
            return {"status": "error", "message": str(e)}
